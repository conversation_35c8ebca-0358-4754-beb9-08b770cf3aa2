{"name": "artsavinglifes", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "cli": "ts-node -r tsconfig-paths/register src/cli.ts", "cli:build": "nest build && node dist/cli.js"}, "dependencies": {"@adminjs/express": "^6.1.1", "@adminjs/nestjs": "^6.1.0", "@adminjs/typeorm": "^5.0.1", "@nestjs/common": "^11.0.16", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.16", "@nestjs/jwt": "^11.0.0", "@nestjs/platform-express": "^11.0.16", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "adminjs": "^7.8.16", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.4.5", "express-formidable": "^1.2.0", "express-session": "^1.18.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.11.4", "nest-commander": "^3.17.0", "nodemailer": "^7.0.3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.4.9", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^11.0.16", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.0", "@types/express-session": "^1.18.1", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.3.1", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}