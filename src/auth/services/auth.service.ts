import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UsersService } from '../../users/users.service';
import { User } from '../../users/entities/user.entity';
import { SigninDto } from '../dto/signin.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
    private configService: ConfigService,
  ) {}

  /**
   * Validate user credentials and return a JWT token
   * @param signinDto The signin credentials
   * @returns The authenticated user and JWT token
   */
  async signIn(signinDto: SigninDto): Promise<{ user: Partial<User>; accessToken: string }> {
    const { email, password, role } = signinDto;
    
    // Validate user credentials
    const user = await this.usersService.validateUserPassword(email, password);
    
    if (!user) {
      this.logger.warn(`Failed login attempt for email: ${email}`);
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check if user is verified
    if (!user.verifiedAt) {
      this.logger.warn(`Unverified user attempted to login: ${email}`);
      throw new UnauthorizedException('Please verify your email before signing in');
    }

    // Check if user is active
    if (!user.isActive) {
      this.logger.warn(`Inactive user attempted to login: ${email}`);
      throw new UnauthorizedException('Your account is inactive');
    }

    // If role is specified, check if user has that role
    if (role) {
      const hasRole = user.roles.some(userRole => userRole.name === role);
      if (!hasRole) {
        this.logger.warn(`User ${email} attempted to login with role ${role} but doesn't have it`);
        throw new UnauthorizedException(`You don't have the required role: ${role}`);
      }
    }

    // Generate JWT token
    const payload = { 
      sub: user.id, 
      email: user.email,
      roles: user.roles.map(r => r.name),
    };
    
    const accessToken = this.jwtService.sign(payload);
    
    // Return user (without password) and token
    const { password: _, ...userWithoutPassword } = user;
    
    return {
      user: userWithoutPassword,
      accessToken,
    };
  }

  /**
   * Validate a JWT token and return the user
   * @param token The JWT token
   * @returns The user associated with the token
   */
  async validateToken(token: string): Promise<User> {
    try {
      const payload = this.jwtService.verify(token);
      const user = await this.usersService.findOne(payload.sub);
      return user;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
