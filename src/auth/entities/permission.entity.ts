import { Enti<PERSON>, PrimaryGeneratedColumn, Column, BaseEntity } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity()
export class Permission extends BaseEntity {
  @ApiProperty({
    description: 'The unique identifier of the permission',
    example: 1,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'The name of the permission',
    example: 'manage_users',
  })
  @Column({ unique: true })
  name: string;

  @ApiProperty({
    description: 'A description of the permission',
    example: 'Can manage user accounts',
    nullable: true,
  })
  @Column({ nullable: true })
  description: string;
}
