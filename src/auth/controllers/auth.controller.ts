import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  ConflictException,
  Get,
  Param,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { UsersService } from '../../users/users.service';
import { RolesService } from '../services/roles.service';
import { VerificationService } from '../services/verification.service';
import { AuthService } from '../services/auth.service';
import { SignupDto } from '../dto/signup.dto';
import { SigninDto } from '../dto/signin.dto';
import { ResendVerificationDto } from '../dto/resend-verification.dto';
import { User } from '../../users/entities/user.entity';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
    private readonly verificationService: VerificationService,
    private readonly authService: AuthService,
  ) {}

  @Post('signup')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Register a new user account' })
  @ApiBody({ type: SignupDto })
  @ApiResponse({
    status: 201,
    description:
      'The user has been successfully registered and a verification email has been sent.',
    type: User,
    schema: {
      properties: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'number' },
            email: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            isActive: { type: 'boolean' },
            verifiedAt: { type: 'string', format: 'date-time', nullable: true },
            roles: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number' },
                  name: { type: 'string' },
                },
              },
            },
          },
        },
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Bad request - validation error.' })
  @ApiResponse({ status: 409, description: 'Conflict - email already exists.' })
  async signup(@Body() signupDto: SignupDto): Promise<{
    user: Partial<User>;
    message: string;
  }> {
    try {
      // Check if user with this email already exists
      try {
        await this.usersService.findByEmail(signupDto.email);
        // If we get here, the user exists
        throw new ConflictException('User with this email already exists');
      } catch (error) {
        // If it's a NotFoundException, that's good - the user doesn't exist
        if (error instanceof ConflictException) {
          throw error;
        }
        // Otherwise, continue with user creation
      }

      // Find the default user role
      let userRole: any;
      try {
        userRole = await this.rolesService.findByName('user');
      } catch (error) {
        // If user role doesn't exist, user will be created without a role
      }

      // Create the user with the user role if it exists
      const user = await this.usersService.create({
        ...signupDto,
        roleIds: userRole ? [userRole.id] : [],
      });

      // Create a verification token and send verification email
      await this.verificationService.createVerificationToken(user.id);

      // Return the user (without password) and a success message
      const userResponse = {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        verifiedAt: user.verifiedAt,
        roles: user.roles,
      };

      return {
        user: userResponse,
        message:
          'User registered successfully. Please check your email to verify your account.',
      };
    } catch (error) {
      throw error;
    }
  }

  @Post('signin')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Sign in to an existing account' })
  @ApiBody({ type: SigninDto })
  @ApiResponse({
    status: 200,
    description: 'User has been successfully authenticated.',
    schema: {
      properties: {
        user: {
          type: 'object',
          properties: {
            id: { type: 'number' },
            email: { type: 'string' },
            firstName: { type: 'string' },
            lastName: { type: 'string' },
            isActive: { type: 'boolean' },
            verifiedAt: { type: 'string', format: 'date-time', nullable: true },
            roles: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'number' },
                  name: { type: 'string' },
                },
              },
            },
          },
        },
        accessToken: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - invalid credentials or unverified account.',
  })
  async signin(
    @Body() signinDto: SigninDto,
  ): Promise<{ user: Partial<User>; accessToken: string }> {
    return this.authService.signIn(signinDto);
  }

  @Post('resend-verification')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resend verification email' })
  @ApiBody({ type: ResendVerificationDto })
  @ApiResponse({
    status: 200,
    description: 'Verification email has been resent.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - email already verified.',
  })
  @ApiResponse({ status: 404, description: 'Not found - user not found.' })
  async resendVerification(
    @Body() resendVerificationDto: ResendVerificationDto,
  ): Promise<{ message: string }> {
    try {
      await this.verificationService.resendVerificationEmail(
        resendVerificationDto.email,
      );
      return {
        message: 'Verification email has been resent. Please check your inbox.',
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.message === 'This email is already verified') {
        throw new BadRequestException(error.message);
      }
      throw new BadRequestException('Failed to resend verification email');
    }
  }

  @Get('verify/:token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify a user account using a verification token' })
  @ApiParam({ name: 'token', description: 'The verification token' })
  @ApiResponse({
    status: 200,
    description: 'The user has been successfully verified.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - invalid or expired token.',
  })
  @ApiResponse({ status: 404, description: 'Not found - token not found.' })
  async verifyAccount(
    @Param('token') token: string,
  ): Promise<{ message: string }> {
    try {
      await this.verificationService.verifyToken(token);
      return { message: 'Account verified successfully' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }
}
