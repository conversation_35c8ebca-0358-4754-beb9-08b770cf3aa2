import { IsE<PERSON>, <PERSON>S<PERSON>, <PERSON>N<PERSON>E<PERSON><PERSON>, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SigninDto {
  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The password for the user account',
    example: 'Password123!',
  })
  @IsString()
  @IsNotEmpty()
  password: string;

  @ApiPropertyOptional({
    description: 'The role to authenticate as (optional, for admin authentication)',
    example: 'admin',
  })
  @IsString()
  @IsOptional()
  role?: string;
}
