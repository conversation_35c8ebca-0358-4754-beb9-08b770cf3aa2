import { IsString, IsOptional } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdatePermissionDto {
  @ApiPropertyOptional({
    description: 'The name of the permission',
    example: 'manage_users',
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({
    description: 'A description of the permission',
    example: 'Can manage user accounts',
  })
  @IsString()
  @IsOptional()
  description?: string;
}
