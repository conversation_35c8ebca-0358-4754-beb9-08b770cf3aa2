import { IsString, <PERSON>NotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreatePermissionDto {
  @ApiProperty({
    description: 'The name of the permission',
    example: 'manage_users',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiPropertyOptional({
    description: 'A description of the permission',
    example: 'Can manage user accounts',
  })
  @IsString()
  @IsOptional()
  description?: string;
}
