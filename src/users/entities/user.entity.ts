import {
  <PERSON>ti<PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToMany,
  JoinTable,
  BaseEntity,
  BeforeInsert,
  BeforeUpdate,
} from 'typeorm';
import * as bcrypt from 'bcrypt';
import { ApiProperty, ApiHideProperty } from '@nestjs/swagger';
import { Role } from '../../auth/entities/role.entity';

@Entity()
export class User extends BaseEntity {
  @ApiProperty({
    description: 'The unique identifier of the user',
    example: 1,
  })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({
    description: 'The email address of the user',
    example: '<EMAIL>',
  })
  @Column({ unique: true })
  email: string;

  @ApiHideProperty()
  @Column()
  password: string;

  @ApiProperty({
    description: 'The first name of the user',
    example: '<PERSON>',
  })
  @Column()
  firstName: string;

  @ApiProperty({
    description: 'The last name of the user',
    example: '<PERSON><PERSON>',
  })
  @Column()
  lastName: string;

  @ApiProperty({
    description: 'Whether the user account is active',
    example: true,
  })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({
    description: 'The date when the user verified their email',
    example: '2023-01-01T00:00:00Z',
    nullable: true,
  })
  @Column({ nullable: true, type: 'timestamp' })
  verifiedAt: Date | null;

  @ApiProperty({
    description: 'The roles assigned to the user',
    type: [Role],
  })
  @ManyToMany(() => Role, (role) => role.users)
  @JoinTable({
    name: 'user_roles',
    joinColumn: { name: 'user_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'role_id', referencedColumnName: 'id' },
  })
  roles: Role[];

  // Flag to control password hashing
  private hasPasswordChanged: boolean = false;

  setPassword(password: string) {
    this.password = password;
    this.hasPasswordChanged = true;
  }

  @BeforeInsert()
  @BeforeUpdate()
  async hashPassword() {
    // Only hash the password if it's new or has been changed
    if (this.hasPasswordChanged) {
      const salt = await bcrypt.genSalt(10);
      this.password = await bcrypt.hash(this.password, salt);
      this.hasPasswordChanged = false;
    }
  }

  async validatePassword(password: string): Promise<boolean> {
    return bcrypt.compare(password, this.password);
  }
}
