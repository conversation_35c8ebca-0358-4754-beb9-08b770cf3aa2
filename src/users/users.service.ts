import { Injectable, NotFoundException } from '@nestjs/common';

import { User } from './entities/user.entity';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UserRepository } from './entities/user.repository';

@Injectable()
export class UsersService {
  constructor(private usersRepository: UserRepository) {}

  async findAll(): Promise<User[]> {
    return this.usersRepository.find({ relations: ['roles'] });
  }

  async findOne(id: number): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { id },
      relations: ['roles', 'roles.permissions'],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return user;
  }

  async findByEmail(email: string): Promise<User> {
    const user = await this.usersRepository.findOne({
      where: { email },
      relations: ['roles', 'roles.permissions'],
    });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    return user;
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.usersRepository.create({
      email: createUserDto.email,
      firstName: createUserDto.firstName,
      lastName: createUserDto.lastName,
    });

    // Use the entity method to set password
    user.setPassword(createUserDto.password);

    if (createUserDto.roleIds && createUserDto.roleIds.length > 0) {
      user.roles = createUserDto.roleIds.map((id) => ({ id }) as any);
    }

    return this.usersRepository.save(user);
  }

  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);

    // Update basic fields
    if (updateUserDto.email) user.email = updateUserDto.email;
    if (updateUserDto.firstName) user.firstName = updateUserDto.firstName;
    if (updateUserDto.lastName) user.lastName = updateUserDto.lastName;
    if (updateUserDto.isActive !== undefined)
      user.isActive = updateUserDto.isActive;

    if (updateUserDto.verifiedAt) {
      user.verifiedAt = updateUserDto.verifiedAt;
    }

    // Use the entity method to update password if provided
    if (updateUserDto.password) {
      user.setPassword(updateUserDto.password);
    }

    if (updateUserDto.roleIds) {
      user.roles = updateUserDto.roleIds.map((id) => ({ id }) as any);
    }

    return this.usersRepository.save(user);
  }

  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.usersRepository.remove(user);
  }

  async addRoleToUser(userId: number, roleId: number): Promise<User> {
    const user = await this.findOne(userId);

    if (!user.roles) {
      user.roles = [];
    }

    // Check if role already exists
    const roleExists = user.roles.some((role) => role.id === roleId);

    if (!roleExists) {
      user.roles.push({ id: roleId } as any);
      await this.usersRepository.save(user);
    }

    return this.findOne(userId);
  }

  async removeRoleFromUser(userId: number, roleId: number): Promise<User> {
    const user = await this.findOne(userId);

    if (user.roles) {
      user.roles = user.roles.filter((role) => role.id !== roleId);
      await this.usersRepository.save(user);
    }

    return this.findOne(userId);
  }

  async validateUserPassword(
    email: string,
    password: string,
  ): Promise<User | null> {
    try {
      const user = await this.usersRepository.findOne({
        where: { email },
        relations: ['roles', 'roles.permissions'],
      });

      if (user && (await user.validatePassword(password))) {
        return user;
      }

      return null;
    } catch (error) {
      return null;
    }
  }
}
