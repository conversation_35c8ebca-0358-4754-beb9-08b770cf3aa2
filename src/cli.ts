import { CommandFactory } from 'nest-commander';
import { CommandsModule } from './commands/commands.module';

/*# Create a permission
npm run cli -- create:permission -n "manage_users" -d "Can manage users"

# Create a role
npm run cli -- create:role -n "editor" -d "Content editor" -p "1,2,3"

# Create a user
npm run cli -- create:user -e "<EMAIL>" -p "Password123!" -f "John" -l "Doe" -r "1,2"

# Seed admin user, roles and permissions
npm run cli -- seed:admin

# Test database connection
npm run cli -- test:db
*/

async function bootstrap() {
  try {
    console.log('Starting CLI application...');
    await CommandFactory.run(CommandsModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });
    console.log('Command executed successfully');
  } catch (error) {
    console.error('Failed to execute command:', error);
    process.exit(1);
  }
}

bootstrap();
