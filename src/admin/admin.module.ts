import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';

// Import your entities
import { User } from '../users/entities/user.entity';
import { Role } from '../auth/entities/role.entity';
import { Permission } from '../auth/entities/permission.entity';
import { VerificationToken } from '../auth/entities/verification-token.entity';

// Import your services
import { UsersService } from '../users/users.service';
import { UserRepository } from '../users/entities/user.repository';

export const dynamicImport = async (packageName: string) =>
  new Function(`return import('${packageName}')`)();

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role, Permission, VerificationToken]),
    (async function () {
      const AdminJS = await dynamicImport('adminjs');
      const AdminJSTypeorm = await dynamicImport('@adminjs/typeorm');
      AdminJS.default.registerAdapter({
        Resource: AdminJSTypeorm.Resource,
        Database: AdminJSTypeorm.Database,
      });
      const AdminModule = await dynamicImport('@adminjs/nestjs');

      return AdminModule.createAdminAsync({
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          adminJsOptions: {
            rootPath: '/admin',
            resources: [
              {
                resource: User,
                options: {
                  navigation: {
                    name: 'User Management',
                    icon: 'User',
                  },
                  properties: {
                    password: {
                      isVisible: {
                        list: false,
                        filter: false,
                        show: false,
                        edit: true,
                      },
                      type: 'password',
                    },
                    verifiedAt: {
                      isVisible: {
                        list: true,
                        filter: true,
                        show: true,
                        edit: false,
                      },
                    },
                  },
                  actions: {
                    new: {
                      before: async (request: any) => {
                        if (request.payload && request.payload.password) {
                          // Create a temporary user to hash the password
                          const user = new User();
                          user.setPassword(request.payload.password);
                          await user.hashPassword();
                          request.payload.password = user.password;
                        }
                        return request;
                      },
                    },
                    edit: {
                      before: async (request: any) => {
                        if (request.payload && request.payload.password) {
                          // Create a temporary user to hash the password
                          const user = new User();
                          user.setPassword(request.payload.password);
                          await user.hashPassword();
                          request.payload.password = user.password;
                        }
                        return request;
                      },
                    },
                  },
                },
              },
              {
                resource: Role,
                options: {
                  navigation: {
                    name: 'User Management',
                    icon: 'User',
                  },
                },
              },
              {
                resource: Permission,
                options: {
                  navigation: {
                    name: 'User Management',
                    icon: 'User',
                  },
                },
              },
              {
                resource: VerificationToken,
                options: {
                  navigation: {
                    name: 'User Management',
                    icon: 'User',
                  },
                },
              },
            ],
            branding: {
              companyName: 'Art Saving Lifes Admin',
              logo: false,
              softwareBrothers: false,
            },
            auth: {
              authenticate: async (email: string, password: string) => {
                // This is just a placeholder. The actual authentication will be handled
                // by the UsersService which is properly injected in the providers section.
                // For AdminJS to work, we need to return a simple object with email and id.
                return { email, id: '1' };
              },
              cookieName: 'adminjs',
              cookiePassword: configService.get<string>('JWT_SECRET'),
            },
            sessionOptions: {
              resave: false,
              saveUninitialized: false,
              secret: configService.get<string>('JWT_SECRET'),
            },
          },
        }),
      });
    })(),
  ],
  providers: [
    {
      provide: UserRepository,
      useFactory: (dataSource: DataSource) => {
        return new UserRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: UsersService,
      useFactory: (userRepository: UserRepository) => {
        return new UsersService(userRepository);
      },
      inject: [UserRepository],
    },
  ],
})
export class AdminJsModule {}
