import { Command, CommandRunner } from 'nest-commander';
import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
@Command({
  name: 'test:db',
  description: 'Test database connection',
})
export class TestDbCommand extends CommandRunner {
  constructor(@InjectDataSource() private dataSource: DataSource) {
    super();
  }

  async run(): Promise<void> {
    try {
      console.log('Testing database connection...');

      // Check if connection is established
      if (this.dataSource.isInitialized) {
        console.log('Database connection is established!');
        console.log('Connection details:', {
          type: this.dataSource.options.type,
          database: this.dataSource.options.database,
          host: (this.dataSource.options as any).host,
          port: (this.dataSource.options as any).port,
        });

        // Try to execute a simple query
        const result = await this.dataSource.query('SELECT 1 as test');
        console.log('Query test result:', result);

        console.log('Database connection test successful!');
      } else {
        console.error('Database connection is not initialized!');
      }
    } catch (error) {
      console.error('Database connection test failed:', error);
      throw error;
    }
  }
}
