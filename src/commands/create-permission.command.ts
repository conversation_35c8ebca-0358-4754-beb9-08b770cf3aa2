import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable } from '@nestjs/common';
import { PermissionsService } from '../auth/services/permissions.service';
import { CreatePermissionDto } from '../auth/dto/create-permission.dto';

@Injectable()
@Command({ name: 'create:permission', description: 'Create a new permission' })
export class CreatePermissionCommand extends CommandRunner {
  constructor(private readonly permissionsService: PermissionsService) {
    super();
  }

  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    try {
      const { name, description } = options;
      
      const createPermissionDto: CreatePermissionDto = {
        name,
        description,
      };
      
      const permission = await this.permissionsService.create(createPermissionDto);
      console.log(`Permission created successfully: ${JSON.stringify(permission)}`);
    } catch (error) {
      console.error('Failed to create permission:', error.message);
    }
  }

  @Option({
    flags: '-n, --name <name>',
    description: 'The name of the permission',
    required: true,
  })
  parseName(val: string): string {
    return val;
  }

  @Option({
    flags: '-d, --description <description>',
    description: 'The description of the permission',
  })
  parseDescription(val: string): string {
    return val;
  }
}