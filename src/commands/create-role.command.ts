import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable } from '@nestjs/common';
import { RolesService } from '../auth/services/roles.service';
import { CreateRoleDto } from '../auth/dto/create-role.dto';

@Injectable()
@Command({ name: 'create:role', description: 'Create a new role' })
export class CreateRoleCommand extends CommandRunner {
  constructor(private readonly rolesService: RolesService) {
    super();
  }

  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    try {
      const { name, description, permissionIds } = options;
      
      const createRoleDto: CreateRoleDto = {
        name,
        description,
        permissionIds: permissionIds ? permissionIds.split(',').map(Number) : [],
      };
      
      const role = await this.rolesService.create(createRoleDto);
      console.log(`Role created successfully: ${JSON.stringify(role)}`);
    } catch (error) {
      console.error('Failed to create role:', error.message);
    }
  }

  @Option({
    flags: '-n, --name <name>',
    description: 'The name of the role',
    required: true,
  })
  parseName(val: string): string {
    return val;
  }

  @Option({
    flags: '-d, --description <description>',
    description: 'The description of the role',
  })
  parseDescription(val: string): string {
    return val;
  }

  @Option({
    flags: '-p, --permissionIds <permissionIds>',
    description: 'Comma-separated list of permission IDs',
  })
  parsePermissionIds(val: string): string {
    return val;
  }
}