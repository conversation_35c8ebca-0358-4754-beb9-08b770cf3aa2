import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from '../auth/auth.module';
import { UsersModule } from '../users/users.module';
import { CreateRoleCommand } from './create-role.command';
import { CreatePermissionCommand } from './create-permission.command';
import { CreateUserCommand } from './create-user.command';
import { SeedAdminCommand } from './seed-admin.command';
import { TestDbCommand } from './test-db.command';
import typeOrmConfigAsync from '../config/typeorm.config';

let envFile: string;
switch (process.env.NODE_ENV) {
  case 'production':
    envFile = '.env.production.local';
    break;
  case 'test':
    envFile = '.env.test.local';
    break;
  case 'development':
  default:
    envFile = '.env.development.local';
    break;
}

@Module({
  imports: [
    ConfigModule.forRoot({
      envFilePath: envFile,
      isGlobal: true,
    }),
    TypeOrmModule.forRootAsync(typeOrmConfigAsync),
    AuthModule,
    UsersModule,
  ],
  providers: [
    CreateRoleCommand,
    CreatePermissionCommand,
    CreateUserCommand,
    SeedAdminCommand,
    TestDbCommand,
  ],
})
export class CommandsModule {}
