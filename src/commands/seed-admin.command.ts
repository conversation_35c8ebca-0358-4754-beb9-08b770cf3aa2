import { Command, CommandRunner } from 'nest-commander';
import { Injectable } from '@nestjs/common';
import { RolesService } from '../auth/services/roles.service';
import { PermissionsService } from '../auth/services/permissions.service';
import { UsersService } from '../users/users.service';

@Injectable()
@Command({
  name: 'seed:admin',
  description: 'Seed admin user, roles and permissions',
})
export class SeedAdminCommand extends CommandRunner {
  constructor(
    private readonly rolesService: RolesService,
    private readonly permissionsService: PermissionsService,
    private readonly usersService: UsersService,
  ) {
    super();
  }

  async run(): Promise<void> {
    try {
      console.log('Starting admin seed...');

      // Create permissions
      console.log('Creating permissions...');
      const createUsersPermission = await this.permissionsService.create({
        name: 'create:users',
        description: 'Can create users',
      });
      console.log('Created permission:', createUsersPermission);

      const readUsersPermission = await this.permissionsService.create({
        name: 'read:users',
        description: 'Can read users',
      });
      console.log('Created permission:', readUsersPermission);

      const updateUsersPermission = await this.permissionsService.create({
        name: 'update:users',
        description: 'Can update users',
      });
      console.log('Created permission:', updateUsersPermission);

      const deleteUsersPermission = await this.permissionsService.create({
        name: 'delete:users',
        description: 'Can delete users',
      });
      console.log('Created permission:', deleteUsersPermission);

      // Create admin role with all permissions
      console.log('Creating admin role...');
      const adminRole = await this.rolesService.create({
        name: 'admin',
        description: 'Administrator with full access',
        permissionIds: [
          createUsersPermission.id,
          readUsersPermission.id,
          updateUsersPermission.id,
          deleteUsersPermission.id,
        ],
      });
      console.log('Created admin role:', adminRole);

      // Create user role with read permission only
      console.log('Creating user role...');
      const userRole = await this.rolesService.create({
        name: 'user',
        description: 'Regular user with limited access',
        permissionIds: [readUsersPermission.id],
      });
      console.log('Created user role:', userRole);

      // Create admin user
      console.log('Creating admin user...');
      const adminUser = await this.usersService.create({
        email: '<EMAIL>',
        password: 'Admin123!',
        firstName: 'Admin',
        lastName: 'User',
        roleIds: [adminRole.id],
      });

      // Mark admin as verified
      await this.usersService.update(adminUser.id, {
        verifiedAt: new Date(),
      });

      console.log('Created admin user:', adminUser);

      console.log('Admin seed completed successfully!');
      console.log(
        'Admin user created with email: <EMAIL> and password: Admin123!',
      );
    } catch (error) {
      console.error('Failed to seed admin data:', error);
      if (error.stack) {
        console.error('Stack trace:', error.stack);
      }
      throw error; // Re-throw to ensure CLI knows there was an error
    }
  }
}
