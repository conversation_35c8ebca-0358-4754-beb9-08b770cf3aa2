import { Command, CommandRunner, Option } from 'nest-commander';
import { Injectable } from '@nestjs/common';
import { UsersService } from '../users/users.service';
import { CreateUserDto } from '../users/dto/create-user.dto';

@Injectable()
@Command({ name: 'create:user', description: 'Create a new user' })
export class CreateUserCommand extends CommandRunner {
  constructor(private readonly usersService: UsersService) {
    super();
  }

  async run(
    passedParams: string[],
    options?: Record<string, any>,
  ): Promise<void> {
    try {
      const { email, password, firstName, lastName, roleIds } = options;
      
      const createUserDto: CreateUserDto = {
        email,
        password,
        firstName,
        lastName,
        roleIds: roleIds ? roleIds.split(',').map(Number) : [],
      };
      
      const user = await this.usersService.create(createUserDto);
      console.log(`User created successfully: ${JSON.stringify(user)}`);
    } catch (error) {
      console.error('Failed to create user:', error.message);
    }
  }

  @Option({
    flags: '-e, --email <email>',
    description: 'The email of the user',
    required: true,
  })
  parseEmail(val: string): string {
    return val;
  }

  @Option({
    flags: '-p, --password <password>',
    description: 'The password of the user',
    required: true,
  })
  parsePassword(val: string): string {
    return val;
  }

  @Option({
    flags: '-f, --firstName <firstName>',
    description: 'The first name of the user',
    required: true,
  })
  parseFirstName(val: string): string {
    return val;
  }

  @Option({
    flags: '-l, --lastName <lastName>',
    description: 'The last name of the user',
    required: true,
  })
  parseLastName(val: string): string {
    return val;
  }

  @Option({
    flags: '-r, --roleIds <roleIds>',
    description: 'Comma-separated list of role IDs',
  })
  parseRoleIds(val: string): string {
    return val;
  }
}