import { ConfigService } from '@nestjs/config';
import {
  TypeOrmModuleAsyncOptions,
  TypeOrmModuleOptions,
} from '@nestjs/typeorm';

export const typeOrmConfigAsync: TypeOrmModuleAsyncOptions = {
  inject: [ConfigService],
  useFactory: async (
    configService: ConfigService,
  ): Promise<TypeOrmModuleOptions> => {
    return {
      autoLoadEntities: true,
      type: 'mysql',
      database: configService.get<string>('DB_NAME'),
      host: configService.get<string>('DB_HOST') || 'localhost',
      port: parseInt(configService.get<string>('DB_PORT'), 10) || 3306,
      username: configService.get<string>('DB_USER') || 'root',
      password: configService.get<string>('DB_PASSWORD', '') || '',
      synchronize: true,
      // bigNumberStrings: false,
      entities: [__dirname + '/../**/*entity.{js,ts}'],
    };
  },
};

export default typeOrmConfigAsync;
