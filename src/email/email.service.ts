import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    // Initialize the transporter with SMTP configuration
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const host = this.configService.get<string>('EMAIL_HOST');
    const port = this.configService.get<number>('EMAIL_PORT');
    const user = this.configService.get<string>('EMAIL_USER');
    const pass = this.configService.get<string>('EMAIL_PASSWORD');
    const secure = this.configService.get<boolean>('EMAIL_SECURE', false);

    // If we're in development mode and no email config is provided,
    // use Ethereal for testing (https://ethereal.email/)
    if (!host && process.env.NODE_ENV !== 'production') {
      this.createTestAccount();
      return;
    }

    this.transporter = nodemailer.createTransport({
      host,
      port,
      secure,
      auth: {
        user,
        pass,
      },
    });

    // Verify the connection
    this.transporter.verify((error) => {
      if (error) {
        this.logger.error('Failed to connect to email server', error);
      } else {
        this.logger.log('Email server connection established');
      }
    });
  }

  private async createTestAccount() {
    try {
      // Create a test account on Ethereal
      const testAccount = await nodemailer.createTestAccount();

      this.transporter = nodemailer.createTransport({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass,
        },
      });

      this.logger.log(
        `Using test email account: ${testAccount.user} and ${testAccount.pass} (check emails at https://ethereal.email)`,
      );
    } catch (error) {
      this.logger.error('Failed to create test email account', error);
    }
  }

  /**
   * Send an email
   * @param to Recipient email address
   * @param subject Email subject
   * @param html Email content in HTML format
   * @param text Plain text version of the email (optional)
   * @returns Information about the sent email
   */
  async sendEmail(
    to: string,
    subject: string,
    html: string,
    text?: string,
  ): Promise<any> {
    try {
      const from = this.configService.get<string>(
        'EMAIL_FROM',
        '<EMAIL>',
      );

      const mailOptions = {
        from,
        to,
        subject,
        text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML tags for text version if not provided
        html,
      };

      const info = await this.transporter.sendMail(mailOptions);

      // If using Ethereal, log the URL where the email can be viewed
      if (info.messageId && info.testMessageUrl) {
        this.logger.log(`Email sent: ${info.messageId}`);
        this.logger.log(`Preview URL: ${info.testMessageUrl}`);
      } else {
        this.logger.log(`Email sent to ${to}: ${info.messageId}`);
      }

      return info;
    } catch (error) {
      this.logger.error(`Failed to send email to ${to}`, error);
      throw error;
    }
  }

  /**
   * Send a verification email
   * @param to Recipient email address
   * @param token Verification token
   * @param name User's name
   * @returns Information about the sent email
   */
  async sendVerificationEmail(
    to: string,
    token: string,
    name: string,
  ): Promise<any> {
    const subject = 'Verify Your Email Address';

    // In a real application, this would be a link to your frontend
    // that then calls your API to verify the token
    const verificationUrl = `${this.configService.get<string>(
      'FRONTEND_URL',
      'http://localhost:3001',
    )}/auth/verify/${token}`;

    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Welcome to Art Saving Lifes!</h2>
        <p>Hello ${name},</p>
        <p>Thank you for signing up. Please verify your email address by clicking the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 20px; text-decoration: none; border-radius: 4px; font-weight: bold;">
            Verify Email
          </a>
        </div>
        <p>Or copy and paste this link into your browser:</p>
        <p>${verificationUrl}</p>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not sign up for an account, please ignore this email.</p>
        <p>Best regards,<br>The Art Saving Lifes Team</p>
      </div>
    `;

    return this.sendEmail(to, subject, html);
  }
}
