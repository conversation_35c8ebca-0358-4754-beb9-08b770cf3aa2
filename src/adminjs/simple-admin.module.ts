import { <PERSON><PERSON><PERSON>, Controller, Get } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

// Import your entities
import { User } from '../users/entities/user.entity';
import { Role } from '../auth/entities/role.entity';
import { Permission } from '../auth/entities/permission.entity';
import { VerificationToken } from '../auth/entities/verification-token.entity';

// Import your services
import { UsersService } from '../users/users.service';
import { UserRepository } from '../users/entities/user.repository';

@Controller('simple-admin')
export class SimpleAdminController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  async getAdminPage() {
    return { message: 'Simple Admin Dashboard - Coming Soon' };
  }
}

@Module({
  imports: [
    TypeOrmModule.forFeature([User, Role, Permission, VerificationToken]),
  ],
  controllers: [SimpleAdminController],
  providers: [
    {
      provide: UserRepository,
      useFactory: (dataSource: DataSource) => {
        return new UserRepository(dataSource);
      },
      inject: [DataSource],
    },
    {
      provide: UsersService,
      useFactory: (userRepository: UserRepository) => {
        return new UsersService(userRepository);
      },
      inject: [UserRepository],
    },
  ],
})
export class SimpleAdminModule {}
