import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

// Entities
import { User } from '../users/entities/user.entity';
import { Role } from '../auth/entities/role.entity';
import { Permission } from '../auth/entities/permission.entity';
import { VerificationToken } from '../auth/entities/verification-token.entity';

// Services
import { UsersService } from '../users/users.service';
import { UserRepository } from '../users/entities/user.repository';

// Utility
export const dynamicImport = async (packageName: string) =>
  new Function(`return import('${packageName}')`)();

@Module({})
export class AdminJsModule {
  static async forRootAsync(): Promise<DynamicModule> {
    const AdminJS = (await dynamicImport('adminjs')).default;
    const { Database, Resource } = await dynamicImport('@adminjs/typeorm');
    const { createAdminAsync } = await dynamicImport('@adminjs/nestjs');

    AdminJS.registerAdapter({ Database, Resource });

    const { AdminModule } = await createAdminAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        adminJsOptions: {
          rootPath: '/admin',
          resources: [
            {
              resource: User,
              options: {
                navigation: {
                  name: 'User Management',
                  icon: 'User',
                },
                properties: {
                  password: {
                    isVisible: {
                      list: false,
                      filter: false,
                      show: false,
                      edit: true,
                    },
                    type: 'password',
                  },
                  verifiedAt: {
                    isVisible: {
                      list: true,
                      filter: true,
                      show: true,
                      edit: false,
                    },
                  },
                },
                actions: {
                  new: {
                    before: async (request: any) => {
                      if (request.payload?.password) {
                        const user = new User();
                        user.setPassword(request.payload.password);
                        await user.hashPassword();
                        request.payload.password = user.password;
                      }
                      return request;
                    },
                  },
                  edit: {
                    before: async (request: any) => {
                      if (request.payload?.password) {
                        const user = new User();
                        user.setPassword(request.payload.password);
                        await user.hashPassword();
                        request.payload.password = user.password;
                      }
                      return request;
                    },
                  },
                },
              },
            },
            {
              resource: Role,
              options: {
                navigation: { name: 'User Management', icon: 'User' },
              },
            },
            {
              resource: Permission,
              options: {
                navigation: { name: 'User Management', icon: 'User' },
              },
            },
            {
              resource: VerificationToken,
              options: {
                navigation: { name: 'User Management', icon: 'User' },
              },
            },
          ],
          branding: {
            companyName: 'Art Saving Lifes Admin',
            logo: false,
            softwareBrothers: false,
          },
          auth: {
            authenticate: async (email: string, password: string) => {
              // Replace with real authentication logic
              return { email, id: '1' };
            },
            cookieName: 'adminjs',
            cookiePassword: configService.get<string>('JWT_SECRET'),
          },
          sessionOptions: {
            resave: false,
            saveUninitialized: false,
            secret: configService.get<string>('JWT_SECRET'),
          },
        },
      }),
    });

    return {
      module: AdminJsModule,
      imports: [
        ConfigModule,
        TypeOrmModule.forFeature([User, Role, Permission, VerificationToken]),
        AdminModule,
      ],
      providers: [
        {
          provide: UserRepository,
          useFactory: (dataSource: DataSource) =>
            new UserRepository(dataSource),
          inject: [DataSource],
        },
        {
          provide: UsersService,
          useFactory: (userRepository: UserRepository) =>
            new UsersService(userRepository),
          inject: [UserRepository],
        },
      ],
    };
  }
}
