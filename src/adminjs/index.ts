import { ConfigService } from '@nestjs/config';
import { AdminJSOptions } from 'adminjs';
import { Router } from 'express';
import { Permission } from 'src/auth/entities/permission.entity';
import { Role } from 'src/auth/entities/role.entity';
import { VerificationToken } from 'src/auth/entities/verification-token.entity';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';

const dynamicImport = async (packageName: string) =>
  new Function(`return import('${packageName}')`)();

export interface AdminJsWrapperOptions extends AdminJSOptions {
  resources?: string[];
  configService: ConfigService;
  userService: UsersService;
}

export const createAdminJs = async (
  options: AdminJsWrapperOptions,
): Promise<{
  rootPath: string;
  adminRouter: Router;
}> => {
  const configService = options.configService;
  const jwtSecret = configService.get<string>('JWT_SECRET');
  const userService = options.userService;

  const adminJSModule = await dynamicImport('adminjs');
  const AdminJS = adminJSModule.default;

  const adminJsExpress = await dynamicImport('@adminjs/express');
  const AdminJSExpress = adminJsExpress.default;

  const { Database, Resource } = await dynamicImport('@adminjs/typeorm');

  AdminJS.registerAdapter({ Database, Resource });

  const rootPath = options.rootPath ?? '/admin';

  // Create AdminJS instance
  const admin = new AdminJS({
    rootPath,
    resources: [
      {
        resource: User,
        options: {
          navigation: {
            name: 'User Management',
            icon: 'User',
          },
          properties: {
            password: {
              isVisible: {
                list: false,
                filter: false,
                show: false,
                edit: true,
              },
              type: 'password',
            },
            verifiedAt: {
              isVisible: {
                list: true,
                filter: true,
                show: true,
                edit: false,
              },
            },
          },
          actions: {
            new: {
              before: async (request: any) => {
                if (request.payload?.password) {
                  const user = new User();
                  user.setPassword(request.payload.password);
                  await user.hashPassword();
                  request.payload.password = user.password;
                }
                return request;
              },
            },
            edit: {
              before: async (request: any) => {
                if (request.payload?.password) {
                  const user = new User();
                  user.setPassword(request.payload.password);
                  await user.hashPassword();
                  request.payload.password = user.password;
                }
                return request;
              },
            },
          },
        },
      },
      {
        resource: Role,
        options: {
          navigation: { name: 'User Management', icon: 'User' },
        },
      },
      {
        resource: Permission,
        options: {
          navigation: { name: 'User Management', icon: 'User' },
        },
      },
      {
        resource: VerificationToken,
        options: {
          navigation: { name: 'User Management', icon: 'User' },
        },
      },
    ],
    branding: {
      companyName: 'Art Saving Lifes Admin',
      logo: false,
      softwareBrothers: false,
    },
    auth: {
      authenticate: async (email: string, password: string) => {
        try {
          const user = await userService.validateUserPassword(email, password);

          if (!user) {
            console.log(`Authentication failed for user: ${email}`);
            return null;
          }

          console.log(`User authenticated successfully: ${user.email}`);
          console.log(`User roles: ${JSON.stringify(user.roles)}`);

          // We already know user exists at this point
          // Check if user has admin role
          const hasAdminRole =
            user.roles &&
            user.roles.some((role: { name: string }) => role.name === 'admin');

          if (!hasAdminRole) {
            console.log('User does not have admin role');
            return null;
          }

          // Return user info for AdminJS
          return {
            email: user.email,
            id: user.id.toString(),
            firstName: user.firstName,
            lastName: user.lastName,
          };
        } catch (error: any) {
          console.error('Error authenticating admin user:', error);
          return null;
        }
      },
      cookieName: 'adminjs',
      cookiePassword: jwtSecret,
    },
  });

  // Build the router with authentication
  const adminRouter = AdminJSExpress.buildAuthenticatedRouter(
    admin,
    {
      authenticate: admin.options.auth?.authenticate || (async () => null),
      cookieName: admin.options.auth?.cookieName || 'adminjs',
      cookiePassword: admin.options.auth?.cookiePassword || jwtSecret,
    },
    null,
    {
      resave: false,
      saveUninitialized: false,
      secret: jwtSecret,
    },
  );

  return {
    rootPath,
    adminRouter,
  };
};
